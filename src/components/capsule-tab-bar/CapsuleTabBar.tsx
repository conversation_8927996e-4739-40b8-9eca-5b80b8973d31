import { type BottomTabBarProps } from '@react-navigation/bottom-tabs';
import {
  type NavigationRoute,
  type ParamListBase,
} from '@react-navigation/native';
import { isNil } from 'es-toolkit';
import { useCallback, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor,
} from 'react-native-reanimated';

type Props = BottomTabBarProps;

const TabBarItem = ({
  isFocused,
  onPress,
  onLongPress,
  options,
  label,
}: {
  isFocused: boolean;
  onPress: () => void;
  onLongPress: () => void;
  options: any;
  label: string;
}) => {
  const animatedValue = useSharedValue(isFocused ? 1 : 0);

  useEffect(() => {
    animatedValue.value = withSpring(isFocused ? 1 : 0, {
      damping: 15,
      stiffness: 150,
    });
  }, [isFocused, animatedValue]);

  const animatedButtonStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      animatedValue.value,
      [0, 1],
      ['transparent', '#007AFF'],
    );

    return {
      backgroundColor,
      transform: [
        {
          scale: withSpring(animatedValue.value === 1 ? 1.05 : 1, {
            damping: 15,
            stiffness: 150,
          }),
        },
      ],
    };
  });

  const animatedTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedValue.value,
      [0, 1],
      ['#666', '#FFFFFF'],
    );

    return {
      color,
      fontWeight: animatedValue.value === 1 ? '600' : '500',
    };
  });

  return (
    <TouchableOpacity
      accessibilityRole="button"
      accessibilityState={isFocused ? { selected: true } : {}}
      accessibilityLabel={options.tabBarAccessibilityLabel}
      onPress={onPress}
      onLongPress={onLongPress}
      style={styles.tabItem}
    >
      <Animated.View style={[styles.capsuleButton, animatedButtonStyle]}>
        <Animated.Text style={[styles.tabLabel, animatedTextStyle]}>
          {label}
        </Animated.Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

export const CapsuleTabBar = (props: Props) => {
  const { state, descriptors, navigation } = props;

  const renderTabBarItem = useCallback(
    (route: NavigationRoute<ParamListBase, string>, index: number) => {
      const { options } = descriptors[route.key];
      const tabBarLabel =
        typeof options.tabBarLabel === 'function'
          ? /* TODO: RoyRao - 后续如果要自定义，再完善实现 */
            options.tabBarLabel({} as any)
          : options.tabBarLabel;
      const label = String(
        !isNil(tabBarLabel)
          ? tabBarLabel
          : !isNil(options.title)
            ? options.title
            : route.name,
      );

      const isFocused = state.index === index;

      const onPress = () => {
        const event = navigation.emit({
          type: 'tabPress',
          target: route.key,
          canPreventDefault: true,
        });

        if (!isFocused && !event.defaultPrevented) {
          navigation.navigate(route.name);
        }
      };

      const onLongPress = () => {
        navigation.emit({
          type: 'tabLongPress',
          target: route.key,
        });
      };

      return (
        <TabBarItem
          key={index}
          isFocused={isFocused}
          onPress={onPress}
          onLongPress={onLongPress}
          options={options}
          label={label}
        />
      );
    },
    [descriptors, navigation, state.index],
  );

  return (
    <SafeAreaView
      edges={['bottom', 'left', 'right']}
      className="bg-background"
      style={styles.tabContainer}
    >
      <View className="bg-white" style={styles.capsuleContainer}>
        {state.routes.map((route, index) => {
          return renderTabBarItem(route, index);
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    // position: 'absolute',
    // bottom: 20,
    // left: 0,
    // right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  capsuleContainer: {
    flexDirection: 'row',
    borderRadius: 30,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tabItem: {
    marginHorizontal: 4,
  },
  capsuleButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: 'transparent',
  },
  capsuleButtonFocused: {
    backgroundColor: '#007AFF',
  },
  tabLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  tabLabelFocused: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
